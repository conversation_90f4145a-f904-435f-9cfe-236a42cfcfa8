#!/usr/bin/env python3
"""
Generate sources.mk for Tongsuo integration with trusty-tee build system.
This script extracts source file information from Tongsuo's Makefile.
"""

import re
import os

def extract_sources_from_makefile():
    """Extract source files from Tongsuo's Makefile."""
    crypto_sources = []
    ssl_sources = []

    with open('Makefile', 'r') as f:
        content = f.read()

    # Find libcrypto.a target and extract object files
    crypto_match = re.search(r'^libcrypto\.a:\s*(.*?)(?=\n\s*ar qc)', content, re.DOTALL | re.MULTILINE)
    if crypto_match:
        crypto_objects = crypto_match.group(1)
        print(f"Found crypto objects section: {len(crypto_objects)} chars")
        # Extract .o files and convert to .c files
        for match in re.finditer(r'(\S+/libcrypto-lib-\S+)\.o', crypto_objects):
            obj_file = match.group(1)
            # Convert object file path to source file path
            src_file = obj_file.replace('/libcrypto-lib-', '/') + '.c'
            if os.path.exists(src_file):
                crypto_sources.append(src_file)
    else:
        print("Could not find libcrypto.a target")

    # Find libssl.a target and extract object files
    ssl_match = re.search(r'^libssl\.a:\s*(.*?)(?=\n\s*ranlib)', content, re.DOTALL | re.MULTILINE)
    if ssl_match:
        ssl_objects = ssl_match.group(1)
        print(f"Found ssl objects section: {len(ssl_objects)} chars")
        # Extract .o files and convert to .c files
        for match in re.finditer(r'(\S+/libssl-lib-\S+)\.o', ssl_objects):
            obj_file = match.group(1)
            # Convert object file path to source file path
            src_file = obj_file.replace('/libssl-lib-', '/') + '.c'
            if os.path.exists(src_file):
                ssl_sources.append(src_file)
    else:
        print("Could not find libssl.a target")

    print(f"Found {len(crypto_sources)} crypto sources and {len(ssl_sources)} ssl sources")
    return crypto_sources, ssl_sources

def generate_sources_mk():
    """Generate sources.mk file."""
    crypto_sources, ssl_sources = extract_sources_from_makefile()
    
    with open('sources.mk', 'w') as f:
        f.write('# Auto-generated sources.mk for Tongsuo\n')
        f.write('# Generated by generate_sources.py\n\n')
        
        f.write('crypto_sources := \\\n')
        for i, src in enumerate(crypto_sources):
            if i == len(crypto_sources) - 1:
                f.write(f'\t{src}\n\n')
            else:
                f.write(f'\t{src} \\\n')
        
        f.write('ssl_sources := \\\n')
        for i, src in enumerate(ssl_sources):
            if i == len(ssl_sources) - 1:
                f.write(f'\t{src}\n\n')
            else:
                f.write(f'\t{src} \\\n')
        
        f.write('tongsuo_sources := $(crypto_sources) $(ssl_sources)\n')

if __name__ == '__main__':
    generate_sources_mk()
    print("Generated sources.mk successfully")
