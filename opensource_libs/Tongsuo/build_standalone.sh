#!/bin/bash

# Tongsuo 单独构建脚本
# 用于生成集成构建所需的文件，包括源文件列表、配置文件等
# 参考官方文档：https://www.tongsuo.net/docs/compilation/source-compilation

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== Tongsuo 单独构建脚本 ==="
echo "目标：生成集成构建所需的文件"
echo "工作目录：$SCRIPT_DIR"

# 清理之前的构建文件
echo "清理之前的构建文件..."
if [ -f "Makefile" ]; then
    make clean || true
fi
rm -f configdata.pm
rm -f include/openssl/opensslconf.h
rm -f include/openssl/configuration.h
rm -f include/crypto/bn_conf.h
rm -f include/crypto/dso_conf.h
rm -f include/openssl/symbol_prefix.h
rm -f crypto/buildinf.h
rm -f libcrypto.a libssl.a
rm -f providers/libcommon.a providers/libdefault.a providers/liblegacy.a

# Tongsuo 官方构建配置
# 针对 trusty-tee 环境的特殊配置
echo "配置 Tongsuo 构建选项..."

# 基础配置选项
CONFIG_OPTIONS=(
    "linux-aarch64"                    # 目标架构
    "no-shared"                        # 静态库
    "no-asm"                          # 禁用汇编优化
    "no-async"                        # 禁用异步IO
    "no-dgram"                        # 禁用数据报
    "no-dso"                          # 禁用动态共享对象
    "no-engine"                       # 禁用引擎
    "no-err"                          # 禁用错误字符串
    "no-filenames"                    # 禁用文件名
    "no-sock"                         # 禁用套接字
    "no-stdio"                        # 禁用标准IO
    "no-ui-console"                   # 禁用控制台UI
    "no-tests"                        # 禁用测试
)

# 禁用不兼容的特性
DISABLE_OPTIONS=(
    "no-delegated-credential"         # 禁用委托凭证
    "no-tls1_3"                      # 禁用TLS 1.3
    "no-quic"                        # 禁用QUIC
)

# 启用国密算法
ENABLE_OPTIONS=(
    "enable-sm2"                     # 启用SM2算法
    "enable-sm3"                     # 启用SM3算法
    "enable-sm4"                     # 启用SM4算法
    "enable-ntls"                    # 启用国密TLS
)

# 符号前缀配置（用于与BoringSSL共存）
SYMBOL_PREFIX="TONGSUO_"

# 构建配置命令
CONFIGURE_CMD="./Configure"
for opt in "${CONFIG_OPTIONS[@]}"; do
    CONFIGURE_CMD="$CONFIGURE_CMD $opt"
done
for opt in "${DISABLE_OPTIONS[@]}"; do
    CONFIGURE_CMD="$CONFIGURE_CMD $opt"
done
for opt in "${ENABLE_OPTIONS[@]}"; do
    CONFIGURE_CMD="$CONFIGURE_CMD $opt"
done

# 添加符号前缀
CONFIGURE_CMD="$CONFIGURE_CMD --symbol-prefix=$SYMBOL_PREFIX"

# 设置安装目录（临时目录）
CONFIGURE_CMD="$CONFIGURE_CMD --prefix=/tmp/tongsuo-build"
CONFIGURE_CMD="$CONFIGURE_CMD --openssldir=/tmp/tongsuo-build/ssl"

echo "执行配置命令："
echo "$CONFIGURE_CMD"
eval "$CONFIGURE_CMD"

# 检查配置结果
echo "检查配置结果..."
if [ ! -f "configdata.pm" ]; then
    echo "错误：配置失败，未生成 configdata.pm"
    exit 1
fi

# 显示配置摘要
echo "配置摘要："
perl configdata.pm --dump | head -20

# 编译库文件
echo "编译 Tongsuo 库文件..."
# 设置编译器标志以禁用API兼容性检查
export CPPFLAGS="-DOPENSSL_API_COMPAT=0x30000000L"
make -j$(nproc) build_libs

# 检查编译结果
echo "检查编译结果..."
if [ ! -f "libcrypto.a" ] || [ ! -f "libssl.a" ]; then
    echo "错误：编译失败，未生成库文件"
    exit 1
fi

echo "库文件大小："
ls -lh libcrypto.a libssl.a

# 生成源文件列表
echo "生成源文件列表..."
python3 generate_sources.py

# 检查生成的源文件列表
if [ ! -f "sources.mk" ]; then
    echo "错误：未生成 sources.mk 文件"
    exit 1
fi

echo "源文件统计："
grep -c "\.c" sources.mk || echo "无法统计源文件数量"

# 验证关键文件
echo "验证生成的关键文件..."
REQUIRED_FILES=(
    "configdata.pm"
    "include/openssl/opensslconf.h"
    "include/openssl/configuration.h"
    "include/crypto/bn_conf.h"
    "include/crypto/dso_conf.h"
    "crypto/buildinf.h"
    "libcrypto.a"
    "libssl.a"
    "sources.mk"
)

MISSING_FILES=()
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "警告：以下文件缺失："
    for file in "${MISSING_FILES[@]}"; do
        echo "  - $file"
    done
fi

# 检查符号前缀
if [ -f "include/openssl/symbol_prefix.h" ]; then
    echo "符号前缀文件已生成："
    head -10 include/openssl/symbol_prefix.h
else
    echo "警告：符号前缀文件未生成"
fi

echo "=== Tongsuo 单独构建完成 ==="
echo "生成的文件可用于 trusty-tee 集成构建"
echo "主要输出文件："
echo "  - libcrypto.a, libssl.a: 编译好的库文件"
echo "  - sources.mk: 源文件列表"
echo "  - configdata.pm: 配置数据"
echo "  - include/: 头文件目录"
echo "  - crypto/buildinf.h: 构建信息头文件"
